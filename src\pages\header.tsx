import { useState, useEffect, useRef } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';


import { 
  User, 
  Mail, 
  ShoppingCart, 
  Menu, 
  X, 
  Shield, 
  Wallet, 
  Store, 
  History,
  Bell
} from 'lucide-react';

// Interface for navigation link items
interface NavLinkItem {
  name: string;
  route: string;
}

// Main Header Component
const Header = () => {
  const location = useLocation();
  const currentPath = location.pathname;
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const profileRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (profileRef.current && !profileRef.current.contains(event.target as Node)) {
        setIsProfileOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [profileRef]);

  const navLinks: NavLinkItem[] = [
    { name: 'MAIN PAGE', route: '/home' },
    { name: 'TRUSTED SELLER', route: '/trusted-seller' },
    { name: 'AUTO ESCROW', route: '/auto-escrow' },
    { name: 'NEWS', route: '/news' },
  ];

  return (
    <header className="sticky top-0 z-50 w-full border-b border-slate-700/30" style={{ backgroundColor: '#101828' }}>
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Left side: User Profile */}
          <div className="flex items-center space-x-4">
            <div className="relative" ref={profileRef}>
              <Button 
                variant="ghost" 
                className="relative h-10 w-10 rounded-full p-0 hover:bg-slate-800/50 cursor-pointer"
                onClick={() => setIsProfileOpen(!isProfileOpen)}
              >
                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-br from-blue-500 to-purple-600 shadow-lg">
                  <User className="h-5 w-5 text-white" />
                </div>
                <div className="absolute -bottom-0.5 -right-0.5 h-3 w-3 rounded-full bg-green-500 border-2 border-slate-800 animate-pulse" />
              </Button>
              {isProfileOpen && (
                <div
                  className="absolute left-0 mt-2 w-64 rounded-md shadow-lg py-1 z-50 border border-slate-700/30"
                  style={{ backgroundColor: '#101828' }}
                >
                  <div className="px-4 py-2">
                    <p className="text-sm font-medium leading-none text-white">Dr. Sharon</p>
                    <p className="text-xs leading-none text-slate-400">
                      Available Balance: <span className="text-green-400 font-semibold">$0.23</span>
                    </p>
                  </div>
                  <div className="border-t border-slate-700/50 my-1" />
                  <Link to="/profile" className="flex items-center px-4 py-2 text-sm text-slate-300 hover:bg-slate-800/50 hover:text-white" onClick={() => setIsProfileOpen(false)}>
                    <User className="mr-2 h-4 w-4" />
                    <span>Personal Account</span>
                  </Link>
                  <Link to="/purchase" className="flex items-center px-4 py-2 text-sm text-slate-300 hover:bg-slate-800/50 hover:text-white" onClick={() => setIsProfileOpen(false)}>
                    <History className="mr-2 h-4 w-4" />
                    <span>Purchase History</span>
                  </Link>
                  <Link to="/account-security" className="flex items-center px-4 py-2 text-sm text-slate-300 hover:bg-slate-800/50 hover:text-white" onClick={() => setIsProfileOpen(false)}>
                    <Shield className="mr-2 h-4 w-4" />
                    <span>Account Security</span>
                  </Link>
                  <Link to="/message" className="flex items-center px-4 py-2 text-sm text-slate-300 hover:bg-slate-800/50 hover:text-white" onClick={() => setIsProfileOpen(false)}>
                    <Mail className="mr-2 h-4 w-4" />
                    <span>Messages</span>
                  </Link>
                  <Link to="/walletpage" className="flex items-center px-4 py-2 text-sm text-slate-300 hover:bg-slate-800/50 hover:text-white" onClick={() => setIsProfileOpen(false)}>
                    <Wallet className="mr-2 h-4 w-4" />
                    <span>Wallet</span>
                  </Link>
                  <Link to="/mystore" className="flex items-center px-4 py-2 text-sm text-slate-300 hover:bg-slate-800/50 hover:text-white" onClick={() => setIsProfileOpen(false)}>
                    <Store className="mr-2 h-4 w-4" />
                    <span>My Store</span>
                  </Link>
                </div>
              )}
            </div>
            
                         <div className="hidden md:block">
               <div className="flex flex-col">
                 <span className="text-sm font-medium text-white">Dr. Sharon</span>
                 <div className="flex items-center space-x-1">
                   <span className="text-xs text-slate-400">Balance:</span>
                   <span className="text-sm font-semibold text-green-400">$0.23</span>
                 </div>
               </div>
             </div>
          </div>

                     {/* Center: Navigation */}
           <nav className="hidden lg:flex items-center space-x-6">
             {navLinks.map((link) => (
               <Link
                 key={link.name}
                 to={link.route}
                 className={`text-sm font-medium transition-colors duration-200 ${
                   currentPath === link.route
                     ? 'text-blue-400 border-b-2 border-blue-400 pb-1'
                     : 'text-slate-400 hover:text-white'
                 }`}
               >
                 {link.name}
               </Link>
             ))}
           </nav>

                     {/* Right side: Actions */}
           <div className="flex items-center space-x-2">
             {/* Messages */}
             <Button variant="ghost" size="icon" className="relative text-slate-300 hover:text-white hover:bg-slate-800/50 h-10 w-10">
               <Mail className="h-5 w-5" />
               <Badge 
                 variant="destructive" 
                 className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs font-bold"
               >
                 3
               </Badge>
             </Button>

             {/* Cart */}
             <Button variant="ghost" size="icon" className="relative text-slate-300 hover:text-white hover:bg-slate-800/50 h-10 w-10">
               <ShoppingCart className="h-5 w-5" />
               <Badge 
                 variant="destructive" 
                 className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs font-bold"
               >
                 3
               </Badge>
             </Button>

             {/* Notifications */}
             <Button variant="ghost" size="icon" className="text-slate-300 hover:text-white hover:bg-slate-800/50 h-10 w-10">
               <Bell className="h-5 w-5" />
             </Button>

             {/* Mobile menu button */}
             <Button
               variant="ghost"
               size="icon"
               className="lg:hidden text-slate-300 hover:text-white hover:bg-slate-800/50 h-10 w-10"
               onClick={() => setIsMenuOpen(!isMenuOpen)}
             >
               {isMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
             </Button>
           </div>
        </div>
      </div>

             {/* Mobile Navigation */}
       {isMenuOpen && (
         <div className="lg:hidden border-t border-slate-700/30" style={{ backgroundColor: '#101828' }}>
           <div className="container mx-auto px-4 py-4">
             {/* Main Navigation */}
             <nav className="flex flex-col space-y-4 mb-6">
               {navLinks.map((link) => (
                 <Link
                   key={link.name}
                   to={link.route}
                   className={`text-sm font-medium transition-colors duration-200 ${
                     currentPath === link.route
                       ? 'text-blue-400 border-l-2 border-blue-400 pl-3'
                       : 'text-slate-400 hover:text-white pl-3'
                   }`}
                   onClick={() => setIsMenuOpen(false)}
                 >
                   {link.name}
                 </Link>
               ))}
             </nav>

             {/* Profile Section */}
             <div className="border-t border-slate-700/50 pt-4">
               <h3 className="text-xs font-semibold text-slate-400 uppercase tracking-wider mb-3 pl-3">Profile</h3>
               <nav className="flex flex-col space-y-2">
                 <Link
                   to="/profile"
                   className={`flex items-center text-sm font-medium transition-colors duration-200 ${
                     currentPath === '/profile'
                       ? 'text-blue-400 border-l-2 border-blue-400 pl-3'
                       : 'text-slate-400 hover:text-white pl-3'
                   }`}
                   onClick={() => setIsMenuOpen(false)}
                 >
                   <User className="mr-2 h-4 w-4" />
                   Personal Account
                 </Link>
                 <Link
                   to="/purchase"
                   className={`flex items-center text-sm font-medium transition-colors duration-200 ${
                     currentPath === '/purchase'
                       ? 'text-blue-400 border-l-2 border-blue-400 pl-3'
                       : 'text-slate-400 hover:text-white pl-3'
                   }`}
                   onClick={() => setIsMenuOpen(false)}
                 >
                   <History className="mr-2 h-4 w-4" />
                   Purchase History
                 </Link>
                 <Link
                   to="/account-security"
                   className={`flex items-center text-sm font-medium transition-colors duration-200 ${
                     currentPath === '/account-security'
                       ? 'text-blue-400 border-l-2 border-blue-400 pl-3'
                       : 'text-slate-400 hover:text-white pl-3'
                   }`}
                   onClick={() => setIsMenuOpen(false)}
                 >
                   <Shield className="mr-2 h-4 w-4" />
                   Account Security
                 </Link>
                 <Link
                   to="/message"
                   className={`flex items-center text-sm font-medium transition-colors duration-200 ${
                     currentPath === '/message'
                       ? 'text-blue-400 border-l-2 border-blue-400 pl-3'
                       : 'text-slate-400 hover:text-white pl-3'
                   }`}
                   onClick={() => setIsMenuOpen(false)}
                 >
                   <Mail className="mr-2 h-4 w-4" />
                   Messages
                 </Link>
                 <Link
                   to="/walletpage"
                   className={`flex items-center text-sm font-medium transition-colors duration-200 ${
                     currentPath === '/walletpage'
                       ? 'text-blue-400 border-l-2 border-blue-400 pl-3'
                       : 'text-slate-400 hover:text-white pl-3'
                   }`}
                   onClick={() => setIsMenuOpen(false)}
                 >
                   <Wallet className="mr-2 h-4 w-4" />
                   Wallet
                 </Link>
                 <Link
                   to="/mystore"
                   className={`flex items-center text-sm font-medium transition-colors duration-200 ${
                     currentPath === '/mystore'
                       ? 'text-blue-400 border-l-2 border-blue-400 pl-3'
                       : 'text-slate-400 hover:text-white pl-3'
                   }`}
                   onClick={() => setIsMenuOpen(false)}
                 >
                   <Store className="mr-2 h-4 w-4" />
                   My Store
                 </Link>
               </nav>
             </div>
           </div>
         </div>
       )}
    </header>
  );
};

export default Header;