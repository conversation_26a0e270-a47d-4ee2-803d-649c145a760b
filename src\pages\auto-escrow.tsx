import { useState } from 'react';

// Define type for view selection props
type ViewSelectorProps = {
  setActiveView: (view: string) => void;
}

// Main App Component
export default function App() {
  // State to manage which content view is active
  const [activeView, setActiveView] = useState('home');

  // A simple mapping of views to their content components
  const renderContent = () => {
    switch (activeView) {
      case 'create':
        return <CreateDealView />;
      case 'active':
        return <ActiveDealsView />;
      case 'archive':
        return <ArchiveDealsView />;
      case 'terms':
        return <TermsAndConditionsView />;
      default:
        return <HomeView setActiveView={setActiveView} />;
    }
  };

  return (
    <div className="min-h-screen font-sans text-slate-300 p-4 sm:p-6 lg:p-8" style={{ backgroundColor: '#101828' }}>
      <div className="container mx-auto">
        {/* Main Content */}
        <main className="w-full">
          {activeView !== 'home' && (
            <button
              onClick={() => setActiveView('home')}
              className="mb-6 text-slate-400 hover:text-white transition-colors duration-300 flex items-center gap-2 bg-slate-800 p-2 rounded-lg"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="m15 18-6-6 6-6"/></svg>
              Back to Home
            </button>
          )}
          {renderContent()}
        </main>
      </div>
    </div>
  );
}

// Home View Component (The main screen from the image)
const HomeView = ({ setActiveView }: ViewSelectorProps) => (
  <div className="flex flex-col items-center justify-center animate-fade-in mt-12">
    <h1 className="text-5xl md:text-7xl font-bold text-white tracking-wider uppercase">
      AUTO-ESCROW
    </h1>
    <p className="mt-4 text-lg md:text-xl text-slate-300 tracking-widest uppercase">
      MAKE YOUR WORK EASILY
    </p>
    <div className="mt-12 flex flex-col sm:flex-row items-center gap-4">
      <button 
        onClick={() => setActiveView('create')}
        className="w-full sm:w-auto bg-transparent border border-slate-600 hover:border-slate-400 text-white font-semibold py-3 px-8 rounded-lg transition-all duration-300 ease-in-out transform hover:scale-105"
      >
        Create a deal
      </button>
      <button 
        onClick={() => setActiveView('active')}
        className="w-full sm:w-auto bg-transparent border border-slate-600 hover:border-slate-400 text-white font-semibold py-3 px-8 rounded-lg transition-all duration-300 ease-in-out transform hover:scale-105"
      >
        Active deals
      </button>
      <button 
        onClick={() => setActiveView('archive')}
        className="w-full sm:w-auto bg-transparent border border-slate-600 hover:border-slate-400 text-white font-semibold py-3 px-8 rounded-lg transition-all duration-300 ease-in-out transform hover:scale-105"
      >
        Archive deals
      </button>
    </div>
    <div className="mt-8">
      <button 
        onClick={() => setActiveView('terms')}
        className="bg-transparent border border-slate-700 hover:border-slate-500 text-slate-400 hover:text-white font-medium py-3 px-8 rounded-lg transition-all duration-300 ease-in-out"
      >
        Auto-escrow terms and conditions
      </button>
    </div>
  </div>
);

// Create Deal View Component
const CreateDealView = () => {
  const [dealType, setDealType] = useState('goods');
  const [amount, setAmount] = useState('');
  const [description, setDescription] = useState('');
  const [buyerEmail, setBuyerEmail] = useState('');
  const [sellerEmail, setSellerEmail] = useState('');

  return (
    <div className="text-white animate-fade-in max-w-4xl mx-auto">
      <h2 className="text-4xl font-bold mb-8 text-center">Create a New Deal</h2>

      <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-2xl p-8">
        <form className="space-y-6">
          {/* Deal Type Selection */}
          <div>
            <label className="block text-sm font-medium text-slate-300 mb-3">Deal Type</label>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {[
                { value: 'goods', label: 'Physical Goods', icon: '📦' },
                { value: 'digital', label: 'Digital Products', icon: '💻' },
                { value: 'services', label: 'Services', icon: '🛠️' }
              ].map((type) => (
                <button
                  key={type.value}
                  type="button"
                  onClick={() => setDealType(type.value)}
                  className={`p-4 rounded-lg border-2 transition-all duration-300 ${
                    dealType === type.value
                      ? 'border-cyan-500 bg-cyan-500/10 text-cyan-400'
                      : 'border-slate-600 hover:border-slate-500 text-slate-300'
                  }`}
                >
                  <div className="text-2xl mb-2">{type.icon}</div>
                  <div className="font-medium">{type.label}</div>
                </button>
              ))}
            </div>
          </div>

          {/* Amount */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">Deal Amount (USD)</label>
              <input
                type="number"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                placeholder="0.00"
                className="w-full bg-slate-700/50 border border-slate-600 rounded-lg text-white placeholder-slate-400 py-3 px-4 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">Escrow Fee</label>
              <div className="bg-slate-700/30 border border-slate-600 rounded-lg py-3 px-4 text-slate-400">
                ${amount ? (parseFloat(amount) * 0.025).toFixed(2) : '0.00'} (2.5%)
              </div>
            </div>
          </div>

          {/* Participants */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">Buyer Email</label>
              <input
                type="email"
                value={buyerEmail}
                onChange={(e) => setBuyerEmail(e.target.value)}
                placeholder="<EMAIL>"
                className="w-full bg-slate-700/50 border border-slate-600 rounded-lg text-white placeholder-slate-400 py-3 px-4 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">Seller Email</label>
              <input
                type="email"
                value={sellerEmail}
                onChange={(e) => setSellerEmail(e.target.value)}
                placeholder="<EMAIL>"
                className="w-full bg-slate-700/50 border border-slate-600 rounded-lg text-white placeholder-slate-400 py-3 px-4 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">Deal Description</label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Describe what is being bought/sold and any specific terms..."
              rows={4}
              className="w-full bg-slate-700/50 border border-slate-600 rounded-lg text-white placeholder-slate-400 py-3 px-4 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent resize-none"
            />
          </div>

          {/* Submit Button */}
          <div className="flex justify-center pt-4">
            <button
              type="submit"
              className="bg-cyan-500 hover:bg-cyan-600 text-white font-bold py-3 px-8 rounded-lg transition-all duration-300 ease-in-out transform hover:scale-105 shadow-lg shadow-cyan-500/20"
            >
              Create Escrow Deal
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Active Deals View Component
const ActiveDealsView = () => {
  const activeDeals = [
    {
      id: 'ESC-001',
      amount: 250.00,
      type: 'Digital Products',
      buyer: '<EMAIL>',
      seller: '<EMAIL>',
      status: 'Pending Payment',
      created: '2023-11-15',
      description: 'Website design package'
    },
    {
      id: 'ESC-002',
      amount: 1200.00,
      type: 'Physical Goods',
      buyer: '<EMAIL>',
      seller: '<EMAIL>',
      status: 'Awaiting Delivery',
      created: '2023-11-12',
      description: 'Gaming laptop'
    },
    {
      id: 'ESC-003',
      amount: 75.00,
      type: 'Services',
      buyer: '<EMAIL>',
      seller: '<EMAIL>',
      status: 'In Progress',
      created: '2023-11-10',
      description: 'Logo design service'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Pending Payment': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'Awaiting Delivery': return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'In Progress': return 'bg-green-500/20 text-green-400 border-green-500/30';
      default: return 'bg-slate-500/20 text-slate-400 border-slate-500/30';
    }
  };

  return (
    <div className="text-white animate-fade-in">
      <h2 className="text-4xl font-bold mb-8">Your Active Deals</h2>

      <div className="grid gap-6">
        {activeDeals.map((deal) => (
          <div key={deal.id} className="bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-2xl p-6 hover:border-slate-600 transition-colors">
            <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
              <div className="flex-1">
                <div className="flex items-center gap-4 mb-3">
                  <h3 className="text-xl font-bold text-white">Deal #{deal.id}</h3>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(deal.status)}`}>
                    {deal.status}
                  </span>
                </div>
                <p className="text-slate-300 mb-2">{deal.description}</p>
                <div className="flex flex-wrap gap-4 text-sm text-slate-400">
                  <span>Type: {deal.type}</span>
                  <span>Created: {new Date(deal.created).toLocaleDateString()}</span>
                  <span>Buyer: {deal.buyer}</span>
                  <span>Seller: {deal.seller}</span>
                </div>
              </div>
              <div className="flex flex-col lg:items-end gap-2">
                <div className="text-2xl font-bold text-green-400">${deal.amount.toFixed(2)}</div>
                <button className="bg-cyan-500 hover:bg-cyan-600 text-white px-4 py-2 rounded-lg transition-colors">
                  View Details
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Archive Deals View Component
const ArchiveDealsView = () => {
  const archivedDeals = [
    {
      id: 'ESC-098',
      amount: 450.00,
      type: 'Digital Products',
      buyer: '<EMAIL>',
      seller: '<EMAIL>',
      status: 'Completed',
      completed: '2023-11-08',
      description: 'Mobile app development'
    },
    {
      id: 'ESC-097',
      amount: 180.00,
      type: 'Physical Goods',
      buyer: '<EMAIL>',
      seller: '<EMAIL>',
      status: 'Completed',
      completed: '2023-11-05',
      description: 'Vintage camera'
    },
    {
      id: 'ESC-096',
      amount: 320.00,
      type: 'Services',
      buyer: '<EMAIL>',
      seller: '<EMAIL>',
      status: 'Cancelled',
      completed: '2023-11-02',
      description: 'Content writing package'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'Cancelled': return 'bg-red-500/20 text-red-400 border-red-500/30';
      default: return 'bg-slate-500/20 text-slate-400 border-slate-500/30';
    }
  };

  return (
    <div className="text-white animate-fade-in">
      <h2 className="text-4xl font-bold mb-8">Archived Deals</h2>

      <div className="grid gap-6">
        {archivedDeals.map((deal) => (
          <div key={deal.id} className="bg-slate-800/30 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6">
            <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
              <div className="flex-1">
                <div className="flex items-center gap-4 mb-3">
                  <h3 className="text-xl font-bold text-slate-300">Deal #{deal.id}</h3>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(deal.status)}`}>
                    {deal.status}
                  </span>
                </div>
                <p className="text-slate-400 mb-2">{deal.description}</p>
                <div className="flex flex-wrap gap-4 text-sm text-slate-500">
                  <span>Type: {deal.type}</span>
                  <span>Completed: {new Date(deal.completed).toLocaleDateString()}</span>
                  <span>Buyer: {deal.buyer}</span>
                  <span>Seller: {deal.seller}</span>
                </div>
              </div>
              <div className="flex flex-col lg:items-end gap-2">
                <div className="text-2xl font-bold text-slate-400">${deal.amount.toFixed(2)}</div>
                <button className="bg-slate-600 hover:bg-slate-500 text-white px-4 py-2 rounded-lg transition-colors">
                  View Archive
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Terms and Conditions View Component
const TermsAndConditionsView = () => (
    <div className="text-left max-w-4xl mx-auto text-white animate-fade-in">
        <h2 className="text-4xl font-bold mb-8 text-center">Auto-Escrow Terms and Conditions</h2>

        <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-2xl p-8">
            <div className="space-y-6 text-slate-300">
                <div className="bg-cyan-500/10 border border-cyan-500/30 rounded-lg p-4 mb-6">
                    <p className="text-cyan-400 font-medium">
                        Last Updated: November 15, 2023
                    </p>
                    <p className="text-sm text-slate-300 mt-2">
                        Please read these terms carefully before using our Auto-Escrow service.
                    </p>
                </div>

                <section>
                    <h3 className="text-2xl font-semibold text-white mb-4 flex items-center">
                        <span className="bg-cyan-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">1</span>
                        Introduction
                    </h3>
                    <p className="mb-4">
                        Welcome to Auto-Escrow, a secure transaction platform operated by RMarket. These terms and conditions ("Terms") outline the rules and regulations for the use of our escrow services. By using our service, you agree to be bound by these Terms.
                    </p>
                    <p>
                        Our escrow service acts as a neutral third party to facilitate secure transactions between buyers and sellers, ensuring that funds are only released when all agreed-upon conditions are met.
                    </p>
                </section>

                <section>
                    <h3 className="text-2xl font-semibold text-white mb-4 flex items-center">
                        <span className="bg-cyan-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">2</span>
                        Service Description
                    </h3>
                    <p className="mb-4">
                        Auto-Escrow provides a secure platform for holding funds in a transaction between two or more parties until specified conditions are met. Our services include:
                    </p>
                    <ul className="list-disc list-inside space-y-2 ml-4 mb-4">
                        <li>Secure fund holding and management</li>
                        <li>Transaction monitoring and verification</li>
                        <li>Dispute resolution assistance</li>
                        <li>Automated release of funds upon completion</li>
                        <li>Transaction history and reporting</li>
                    </ul>
                </section>

                <section>
                    <h3 className="text-2xl font-semibold text-white mb-4 flex items-center">
                        <span className="bg-cyan-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">3</span>
                        User Responsibilities
                    </h3>
                    <p className="mb-4">
                        Users are responsible for:
                    </p>
                    <ul className="list-disc list-inside space-y-2 ml-4 mb-4">
                        <li>Providing accurate and complete information</li>
                        <li>Complying with all applicable laws and regulations</li>
                        <li>Maintaining the confidentiality of account credentials</li>
                        <li>Promptly reporting any suspicious activity</li>
                        <li>Ensuring all transaction details are correct before confirmation</li>
                    </ul>
                </section>

                <section>
                    <h3 className="text-2xl font-semibold text-white mb-4 flex items-center">
                        <span className="bg-cyan-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">4</span>
                        Fees and Charges
                    </h3>
                    <p className="mb-4">
                        Our escrow service charges a fee of 2.5% of the transaction amount. This fee covers:
                    </p>
                    <ul className="list-disc list-inside space-y-2 ml-4 mb-4">
                        <li>Secure fund management and storage</li>
                        <li>Transaction processing and verification</li>
                        <li>Customer support and dispute resolution</li>
                        <li>Platform maintenance and security</li>
                    </ul>
                    <p>
                        Fees are automatically deducted from the transaction amount before release to the seller.
                    </p>
                </section>

                <section>
                    <h3 className="text-2xl font-semibold text-white mb-4 flex items-center">
                        <span className="bg-cyan-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">5</span>
                        Dispute Resolution
                    </h3>
                    <p className="mb-4">
                        In case of disputes between parties, Auto-Escrow will:
                    </p>
                    <ul className="list-disc list-inside space-y-2 ml-4 mb-4">
                        <li>Review all available evidence and documentation</li>
                        <li>Communicate with both parties to understand the issue</li>
                        <li>Make a fair determination based on the evidence</li>
                        <li>Release funds according to the resolution decision</li>
                    </ul>
                </section>

                <section>
                    <h3 className="text-2xl font-semibold text-white mb-4 flex items-center">
                        <span className="bg-cyan-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">6</span>
                        Limitation of Liability
                    </h3>
                    <p className="mb-4">
                        Auto-Escrow's liability is limited to the amount of funds held in escrow for the specific transaction. We are not liable for:
                    </p>
                    <ul className="list-disc list-inside space-y-2 ml-4 mb-4">
                        <li>Indirect, incidental, or consequential damages</li>
                        <li>Loss of profits or business opportunities</li>
                        <li>Actions or omissions of transaction parties</li>
                        <li>Technical issues beyond our reasonable control</li>
                    </ul>
                </section>

                <section>
                    <h3 className="text-2xl font-semibold text-white mb-4 flex items-center">
                        <span className="bg-cyan-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">7</span>
                        Privacy and Security
                    </h3>
                    <p className="mb-4">
                        We are committed to protecting your privacy and maintaining the security of your transactions. All personal and financial information is encrypted and stored securely. We do not share your information with third parties except as required by law or for transaction processing.
                    </p>
                </section>

                <section>
                    <h3 className="text-2xl font-semibold text-white mb-4 flex items-center">
                        <span className="bg-cyan-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">8</span>
                        Termination
                    </h3>
                    <p className="mb-4">
                        We reserve the right to terminate or suspend access to our services for violations of these Terms or for any other reason at our sole discretion. Upon termination, any funds held in escrow will be handled according to the status of ongoing transactions.
                    </p>
                </section>

                <div className="bg-slate-700/50 rounded-lg p-6 mt-8">
                    <h4 className="text-lg font-semibold text-white mb-3">Contact Information</h4>
                    <p className="text-slate-300">
                        If you have any questions about these Terms, please contact our support team at:
                    </p>
                    <div className="mt-3 space-y-1">
                        <p>Email: <EMAIL></p>
                        <p>Phone: +1 (555) 123-ESCROW</p>
                        <p>Hours: Monday - Friday, 9:00 AM - 6:00 PM EST</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
);
